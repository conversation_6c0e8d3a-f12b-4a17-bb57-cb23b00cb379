from typing import Any, Optional, Union

from vllm.entrypoints.openai.tool_parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tool<PERSON>arser
from vllm.entrypoints.openai.protocol import ExtractedToolCallInformation
from vllm.entrypoints.openai.protocol import DeltaMessage
from vllm.entrypoints.openai.protocol import ChatCompletionRequest
from vllm.transformers_utils.tokenizer import AnyTokenizer

@ToolParserManager.register_module(["example"])
class ExampleToolParser(ToolParser):
    def __init__(self, tokenizer: AnyTokenizer):
        super().__init__(tokenizer)

    def adjust_request(
            self, request: ChatCompletionRequest) -> ChatCompletionRequest:
        return request

    def extract_tool_calls_streaming(
        self, *args, **kwargs,
    ) -> Union[DeltaMessage, None]:
        return DeltaMessage(
            role="assistant",
            content="",
            tool_calls=[],
        )

    def extract_tool_calls(
        self, *args, **kwargs,
    ) -> ExtractedToolCallInformation:
        # Get the model output text from args
        text = args[0] if args else ""
        return ExtractedToolCallInformation(tools_called=False,
                                            tool_calls=[],
                                            content=text)
