# This file was autogenerated by uv via the following command:
#    uv pip compile requirements/test.in -o requirements/test.txt --index-strategy unsafe-best-match --torch-backend cu128
absl-py==2.1.0
    # via rouge-score
accelerate==1.0.1
    # via
    #   lm-eval
    #   peft
aenum==3.1.16
    # via lightly
affine==2.4.0
    # via rasterio
aiohappyeyeballs==2.4.3
    # via aiohttp
aiohttp==3.10.11
    # via
    #   aiohttp-cors
    #   datasets
    #   fsspec
    #   lm-eval
    #   ray
aiohttp-cors==0.8.1
    # via ray
aiosignal==1.3.1
    # via
    #   aiohttp
    #   ray
albucore==0.0.16
    # via terratorch
albumentations==1.4.6
    # via terratorch
alembic==1.16.4
    # via mlflow
annotated-types==0.7.0
    # via pydantic
antlr4-python3-runtime==4.9.3
    # via
    #   hydra-core
    #   omegaconf
anyio==4.6.2.post1
    # via
    #   httpx
    #   starlette
argcomplete==3.5.1
    # via datamodel-code-generator
arrow==1.3.0
    # via isoduration
attrs==24.2.0
    # via
    #   aiohttp
    #   fiona
    #   hypothesis
    #   jsonlines
    #   jsonschema
    #   pytest-subtests
    #   rasterio
    #   referencing
audioread==3.0.1
    # via librosa
backoff==2.2.1
    # via
    #   -r requirements/test.in
    #   schemathesis
bitsandbytes==0.46.1
    # via
    #   -r requirements/test.in
    #   lightning
black==24.10.0
    # via datamodel-code-generator
blinker==1.9.0
    # via flask
blobfile==3.0.0
    # via -r requirements/test.in
bm25s==0.2.13
    # via mteb
boto3==1.35.57
    # via tensorizer
botocore==1.35.57
    # via
    #   boto3
    #   s3transfer
bounded-pool-executor==0.0.3
    # via pqdm
buildkite-test-collector==0.1.9
    # via -r requirements/test.in
cachetools==5.5.2
    # via
    #   google-auth
    #   mlflow-skinny
certifi==2024.8.30
    # via
    #   fiona
    #   httpcore
    #   httpx
    #   lightly
    #   pyogrio
    #   pyproj
    #   rasterio
    #   requests
cffi==1.17.1
    # via soundfile
chardet==5.2.0
    # via mbstrdecoder
charset-normalizer==3.4.0
    # via requests
click==8.1.7
    # via
    #   black
    #   click-plugins
    #   cligj
    #   fiona
    #   flask
    #   jiwer
    #   mlflow-skinny
    #   nltk
    #   rasterio
    #   ray
    #   schemathesis
    #   typer
    #   uvicorn
click-plugins==*******
    # via
    #   fiona
    #   rasterio
cligj==0.7.2
    # via
    #   fiona
    #   rasterio
cloudpickle==3.1.1
    # via mlflow-skinny
colorama==0.4.6
    # via
    #   sacrebleu
    #   schemathesis
    #   tqdm-multiprocess
colorful==0.5.6
    # via ray
contourpy==1.3.0
    # via matplotlib
cramjam==2.9.0
    # via fastparquet
cupy-cuda12x==13.3.0
    # via ray
cycler==0.12.1
    # via matplotlib
databricks-sdk==0.59.0
    # via mlflow-skinny
datamodel-code-generator==0.26.3
    # via -r requirements/test.in
dataproperty==1.0.1
    # via
    #   pytablewriter
    #   tabledata
datasets==3.0.2
    # via
    #   evaluate
    #   lm-eval
    #   mteb
decorator==5.1.1
    # via librosa
dill==0.3.8
    # via
    #   datasets
    #   evaluate
    #   lm-eval
    #   multiprocess
distlib==0.3.9
    # via virtualenv
dnspython==2.7.0
    # via email-validator
docker==7.1.0
    # via mlflow
docopt==0.6.2
    # via num2words
docstring-parser==0.17.0
    # via jsonargparse
efficientnet-pytorch==0.7.1
    # via segmentation-models-pytorch
einops==0.8.1
    # via
    #   -r requirements/test.in
    #   encodec
    #   mamba-ssm
    #   terratorch
    #   torchgeo
    #   vector-quantize-pytorch
    #   vocos
einx==0.3.0
    # via vector-quantize-pytorch
email-validator==2.2.0
    # via pydantic
encodec==0.1.1
    # via vocos
eval-type-backport==0.2.2
    # via mteb
evaluate==0.4.3
    # via lm-eval
fastapi==0.116.1
    # via mlflow-skinny
fastparquet==2024.11.0
    # via genai-perf
fastrlock==0.8.2
    # via cupy-cuda12x
fastsafetensors==0.1.10
    # via -r requirements/test.in
filelock==3.16.1
    # via
    #   blobfile
    #   datasets
    #   huggingface-hub
    #   ray
    #   torch
    #   transformers
    #   virtualenv
fiona==1.10.1
    # via torchgeo
flask==3.1.1
    # via mlflow
fonttools==4.54.1
    # via matplotlib
fqdn==1.5.1
    # via jsonschema
frozendict==2.4.6
    # via einx
frozenlist==1.5.0
    # via
    #   aiohttp
    #   aiosignal
    #   ray
fsspec==2024.9.0
    # via
    #   datasets
    #   evaluate
    #   fastparquet
    #   huggingface-hub
    #   lightning
    #   pytorch-lightning
    #   torch
ftfy==6.3.1
    # via open-clip-torch
genai-perf==0.0.8
    # via -r requirements/test.in
genson==1.3.0
    # via datamodel-code-generator
geopandas==1.0.1
    # via terratorch
gitdb==4.0.12
    # via gitpython
gitpython==3.1.44
    # via mlflow-skinny
google-api-core==2.24.2
    # via opencensus
google-auth==2.40.2
    # via
    #   databricks-sdk
    #   google-api-core
googleapis-common-protos==1.70.0
    # via google-api-core
graphene==3.4.3
    # via mlflow
graphql-core==3.2.6
    # via
    #   graphene
    #   graphql-relay
    #   hypothesis-graphql
graphql-relay==3.2.0
    # via graphene
greenlet==3.2.3
    # via sqlalchemy
grpcio==1.71.0
    # via ray
gunicorn==23.0.0
    # via mlflow
h11==0.14.0
    # via
    #   httpcore
    #   uvicorn
h5py==3.13.0
    # via terratorch
harfile==0.3.0
    # via schemathesis
hf-xet==1.1.3
    # via huggingface-hub
hiredis==3.0.0
    # via tensorizer
httpcore==1.0.6
    # via httpx
httpx==0.27.2
    # via
    #   -r requirements/test.in
    #   schemathesis
huggingface-hub==0.33.1
    # via
    #   -r requirements/test.in
    #   accelerate
    #   datasets
    #   evaluate
    #   open-clip-torch
    #   peft
    #   segmentation-models-pytorch
    #   sentence-transformers
    #   terratorch
    #   timm
    #   tokenizers
    #   transformers
    #   vocos
humanize==4.11.0
    # via runai-model-streamer
hydra-core==1.3.2
    # via
    #   lightly
    #   lightning
hypothesis==6.131.0
    # via
    #   hypothesis-graphql
    #   hypothesis-jsonschema
    #   schemathesis
hypothesis-graphql==0.11.1
    # via schemathesis
hypothesis-jsonschema==0.23.1
    # via schemathesis
idna==3.10
    # via
    #   anyio
    #   email-validator
    #   httpx
    #   jsonschema
    #   requests
    #   yarl
imageio==2.37.0
    # via scikit-image
importlib-metadata==8.7.0
    # via
    #   mlflow-skinny
    #   opentelemetry-api
importlib-resources==6.5.2
    # via typeshed-client
inflect==5.6.2
    # via datamodel-code-generator
iniconfig==2.0.0
    # via pytest
isoduration==20.11.0
    # via jsonschema
isort==5.13.2
    # via datamodel-code-generator
itsdangerous==2.2.0
    # via flask
jinja2==3.1.6
    # via
    #   datamodel-code-generator
    #   flask
    #   mlflow
    #   torch
jiwer==3.0.5
    # via -r requirements/test.in
jmespath==1.0.1
    # via
    #   boto3
    #   botocore
joblib==1.4.2
    # via
    #   librosa
    #   nltk
    #   scikit-learn
jsonargparse==4.35.0
    # via
    #   lightning
    #   terratorch
jsonlines==4.0.0
    # via lm-eval
jsonpointer==3.0.0
    # via jsonschema
jsonschema==4.23.0
    # via
    #   hypothesis-jsonschema
    #   mistral-common
    #   ray
    #   schemathesis
jsonschema-specifications==2024.10.1
    # via jsonschema
junit-xml==1.9
    # via schemathesis
kaleido==0.2.1
    # via genai-perf
kiwisolver==1.4.7
    # via matplotlib
kornia==0.8.1
    # via torchgeo
kornia-rs==0.1.9
    # via kornia
lazy-loader==0.4
    # via
    #   librosa
    #   scikit-image
libnacl==2.1.0
    # via tensorizer
librosa==0.10.2.post1
    # via -r requirements/test.in
lightly==1.5.20
    # via
    #   terratorch
    #   torchgeo
lightly-utils==0.0.2
    # via lightly
lightning==2.5.1.post0
    # via
    #   terratorch
    #   torchgeo
lightning-utilities==0.14.3
    # via
    #   lightning
    #   pytorch-lightning
    #   torchmetrics
llvmlite==0.44.0
    # via numba
lm-eval==0.4.8
    # via -r requirements/test.in
lxml==5.3.0
    # via
    #   blobfile
    #   sacrebleu
mako==1.3.10
    # via alembic
mamba-ssm==2.2.5
    # via -r requirements/test.in
markdown==3.8.2
    # via mlflow
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.1
    # via
    #   flask
    #   jinja2
    #   mako
    #   werkzeug
matplotlib==3.9.2
    # via
    #   -r requirements/test.in
    #   lightning
    #   mlflow
    #   pycocotools
    #   torchgeo
mbstrdecoder==1.1.3
    # via
    #   dataproperty
    #   pytablewriter
    #   typepy
mdurl==0.1.2
    # via markdown-it-py
mistral-common==1.8.2
    # via -r requirements/test.in
mlflow==2.22.0
    # via terratorch
mlflow-skinny==2.22.0
    # via mlflow
more-itertools==10.5.0
    # via lm-eval
mpmath==1.3.0
    # via sympy
msgpack==1.1.0
    # via
    #   librosa
    #   ray
mteb==1.38.11
    # via -r requirements/test.in
multidict==6.1.0
    # via
    #   aiohttp
    #   yarl
multiprocess==0.70.16
    # via
    #   datasets
    #   evaluate
munch==4.0.0
    # via pretrainedmodels
mypy-extensions==1.0.0
    # via black
networkx==3.2.1
    # via
    #   scikit-image
    #   torch
ninja==********
    # via mamba-ssm
nltk==3.9.1
    # via rouge-score
num2words==0.5.14
    # via -r requirements/test.in
numba==0.61.2
    # via
    #   -r requirements/test.in
    #   librosa
numexpr==2.10.1
    # via lm-eval
numpy==1.26.4
    # via
    #   -r requirements/test.in
    #   accelerate
    #   albucore
    #   albumentations
    #   bitsandbytes
    #   bm25s
    #   contourpy
    #   cupy-cuda12x
    #   datasets
    #   einx
    #   encodec
    #   evaluate
    #   fastparquet
    #   genai-perf
    #   geopandas
    #   h5py
    #   imageio
    #   librosa
    #   lightly
    #   lightly-utils
    #   matplotlib
    #   mistral-common
    #   mlflow
    #   mteb
    #   numba
    #   numexpr
    #   opencv-python-headless
    #   pandas
    #   patsy
    #   peft
    #   pycocotools
    #   pyogrio
    #   rasterio
    #   rioxarray
    #   rouge-score
    #   runai-model-streamer
    #   sacrebleu
    #   scikit-image
    #   scikit-learn
    #   scipy
    #   segmentation-models-pytorch
    #   shapely
    #   soxr
    #   statsmodels
    #   tensorboardx
    #   tensorizer
    #   tifffile
    #   torchgeo
    #   torchmetrics
    #   torchvision
    #   transformers
    #   tritonclient
    #   vocos
    #   xarray
nvidia-cublas-cu12==*********
    # via
    #   nvidia-cudnn-cu12
    #   nvidia-cusolver-cu12
    #   torch
nvidia-cuda-cupti-cu12==12.8.57
    # via torch
nvidia-cuda-nvrtc-cu12==12.8.61
    # via torch
nvidia-cuda-runtime-cu12==12.8.57
    # via torch
nvidia-cudnn-cu12==********
    # via torch
nvidia-cufft-cu12==*********
    # via torch
nvidia-cufile-cu12==*********
    # via torch
nvidia-curand-cu12==*********
    # via torch
nvidia-cusolver-cu12==*********
    # via torch
nvidia-cusparse-cu12==*********
    # via
    #   nvidia-cusolver-cu12
    #   torch
nvidia-cusparselt-cu12==0.6.3
    # via torch
nvidia-nccl-cu12==2.26.2
    # via torch
nvidia-nvjitlink-cu12==12.8.61
    # via
    #   nvidia-cufft-cu12
    #   nvidia-cusolver-cu12
    #   nvidia-cusparse-cu12
    #   torch
nvidia-nvtx-cu12==12.8.55
    # via torch
omegaconf==2.3.0
    # via
    #   hydra-core
    #   lightning
open-clip-torch==2.32.0
    # via -r requirements/test.in
opencensus==0.11.4
    # via ray
opencensus-context==0.1.3
    # via opencensus
opencv-python-headless==*********
    # via
    #   -r requirements/test.in
    #   albucore
    #   albumentations
    #   mistral-common
opentelemetry-api==1.35.0
    # via
    #   mlflow-skinny
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
opentelemetry-sdk==1.35.0
    # via mlflow-skinny
opentelemetry-semantic-conventions==0.56b0
    # via opentelemetry-sdk
packaging==24.2
    # via
    #   accelerate
    #   black
    #   datamodel-code-generator
    #   datasets
    #   evaluate
    #   fastparquet
    #   geopandas
    #   gunicorn
    #   huggingface-hub
    #   hydra-core
    #   kornia
    #   lazy-loader
    #   lightning
    #   lightning-utilities
    #   mamba-ssm
    #   matplotlib
    #   mlflow-skinny
    #   peft
    #   plotly
    #   pooch
    #   pyogrio
    #   pytest
    #   pytest-rerunfailures
    #   pytorch-lightning
    #   ray
    #   rioxarray
    #   scikit-image
    #   statsmodels
    #   tensorboardx
    #   torchmetrics
    #   transformers
    #   typepy
    #   xarray
pandas==2.2.3
    # via
    #   datasets
    #   evaluate
    #   fastparquet
    #   genai-perf
    #   geopandas
    #   mlflow
    #   statsmodels
    #   torchgeo
    #   xarray
pathspec==0.12.1
    # via black
pathvalidate==3.2.1
    # via pytablewriter
patsy==1.0.1
    # via statsmodels
peft==0.16.0
    # via
    #   -r requirements/test.in
    #   lm-eval
pillow==10.4.0
    # via
    #   genai-perf
    #   imageio
    #   lightly-utils
    #   matplotlib
    #   mistral-common
    #   scikit-image
    #   segmentation-models-pytorch
    #   sentence-transformers
    #   torchgeo
    #   torchvision
platformdirs==4.3.6
    # via
    #   black
    #   pooch
    #   virtualenv
plotly==5.24.1
    # via genai-perf
pluggy==1.5.0
    # via pytest
polars==1.29.0
    # via mteb
pooch==1.8.2
    # via librosa
portalocker==2.10.1
    # via sacrebleu
pqdm==0.2.0
    # via -r requirements/test.in
pretrainedmodels==0.7.4
    # via segmentation-models-pytorch
prometheus-client==0.22.0
    # via ray
propcache==0.2.0
    # via yarl
proto-plus==1.26.1
    # via google-api-core
protobuf==5.28.3
    # via
    #   google-api-core
    #   googleapis-common-protos
    #   mlflow-skinny
    #   proto-plus
    #   ray
    #   tensorboardx
    #   tensorizer
psutil==6.1.0
    # via
    #   accelerate
    #   peft
    #   tensorizer
py==1.11.0
    # via pytest-forked
py-spy==0.4.0
    # via ray
pyarrow==18.0.0
    # via
    #   datasets
    #   genai-perf
    #   mlflow
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pybind11==2.13.6
    # via lm-eval
pycocotools==2.0.8
    # via terratorch
pycountry==24.6.1
    # via pydantic-extra-types
pycparser==2.22
    # via cffi
pycryptodomex==3.22.0
    # via blobfile
pydantic==2.11.5
    # via
    #   -r requirements/test.in
    #   albumentations
    #   datamodel-code-generator
    #   fastapi
    #   lightly
    #   mistral-common
    #   mlflow-skinny
    #   mteb
    #   pydantic-extra-types
    #   ray
pydantic-core==2.33.2
    # via pydantic
pydantic-extra-types==2.10.5
    # via mistral-common
pygments==2.18.0
    # via rich
pyogrio==0.11.0
    # via geopandas
pyparsing==3.2.0
    # via
    #   matplotlib
    #   rasterio
pyproj==3.7.1
    # via
    #   geopandas
    #   rioxarray
    #   torchgeo
pyrate-limiter==3.7.0
    # via schemathesis
pystemmer==3.0.0
    # via mteb
pytablewriter==1.2.0
    # via lm-eval
pytest==8.3.5
    # via
    #   -r requirements/test.in
    #   buildkite-test-collector
    #   genai-perf
    #   pytest-asyncio
    #   pytest-forked
    #   pytest-mock
    #   pytest-rerunfailures
    #   pytest-shard
    #   pytest-subtests
    #   pytest-timeout
    #   schemathesis
    #   terratorch
pytest-asyncio==0.24.0
    # via -r requirements/test.in
pytest-forked==1.6.0
    # via -r requirements/test.in
pytest-mock==3.14.0
    # via genai-perf
pytest-rerunfailures==14.0
    # via -r requirements/test.in
pytest-shard==0.1.2
    # via -r requirements/test.in
pytest-subtests==0.14.1
    # via schemathesis
pytest-timeout==2.3.1
    # via -r requirements/test.in
python-box==7.3.2
    # via terratorch
python-dateutil==2.9.0.post0
    # via
    #   arrow
    #   botocore
    #   graphene
    #   lightly
    #   matplotlib
    #   pandas
    #   typepy
python-rapidjson==1.20
    # via tritonclient
pytorch-lightning==2.5.2
    # via
    #   lightly
    #   lightning
pytrec-eval-terrier==0.5.7
    # via mteb
pytz==2024.2
    # via
    #   pandas
    #   typepy
pyyaml==6.0.2
    # via
    #   accelerate
    #   albumentations
    #   datamodel-code-generator
    #   datasets
    #   genai-perf
    #   huggingface-hub
    #   jsonargparse
    #   lightning
    #   mlflow-skinny
    #   omegaconf
    #   peft
    #   pytorch-lightning
    #   ray
    #   responses
    #   schemathesis
    #   timm
    #   transformers
    #   vocos
rapidfuzz==3.12.1
    # via jiwer
rasterio==1.4.3
    # via
    #   rioxarray
    #   terratorch
    #   torchgeo
ray==2.43.0
    # via -r requirements/test.in
redis==5.2.0
    # via tensorizer
referencing==0.35.1
    # via
    #   jsonschema
    #   jsonschema-specifications
regex==2024.9.11
    # via
    #   nltk
    #   open-clip-torch
    #   sacrebleu
    #   tiktoken
    #   transformers
requests==2.32.3
    # via
    #   buildkite-test-collector
    #   databricks-sdk
    #   datasets
    #   docker
    #   evaluate
    #   google-api-core
    #   huggingface-hub
    #   lightly
    #   lm-eval
    #   mistral-common
    #   mlflow-skinny
    #   mteb
    #   pooch
    #   ray
    #   responses
    #   schemathesis
    #   starlette-testclient
    #   tiktoken
    #   transformers
responses==0.25.3
    # via genai-perf
rfc3339-validator==0.1.4
    # via jsonschema
rfc3987==1.3.8
    # via jsonschema
rich==13.9.4
    # via
    #   genai-perf
    #   lightning
    #   mteb
    #   typer
rioxarray==0.19.0
    # via terratorch
rouge-score==0.1.2
    # via lm-eval
rpds-py==0.20.1
    # via
    #   jsonschema
    #   referencing
rsa==4.9.1
    # via google-auth
rtree==1.4.0
    # via torchgeo
runai-model-streamer==0.11.0
    # via -r requirements/test.in
runai-model-streamer-s3==0.11.0
    # via -r requirements/test.in
s3transfer==0.10.3
    # via boto3
sacrebleu==2.4.3
    # via lm-eval
safetensors==0.4.5
    # via
    #   accelerate
    #   open-clip-torch
    #   peft
    #   timm
    #   transformers
schemathesis==3.39.15
    # via -r requirements/test.in
scikit-image==0.25.2
    # via albumentations
scikit-learn==1.5.2
    # via
    #   albumentations
    #   librosa
    #   lm-eval
    #   mlflow
    #   mteb
    #   sentence-transformers
scipy==1.13.1
    # via
    #   albumentations
    #   bm25s
    #   librosa
    #   mlflow
    #   mteb
    #   scikit-image
    #   scikit-learn
    #   sentence-transformers
    #   statsmodels
    #   vocos
segmentation-models-pytorch==0.4.0
    # via
    #   terratorch
    #   torchgeo
sentence-transformers==3.2.1
    # via
    #   -r requirements/test.in
    #   mteb
sentencepiece==0.2.0
    # via mistral-common
setuptools==77.0.3
    # via
    #   lightning-utilities
    #   mamba-ssm
    #   pytablewriter
    #   triton
shapely==2.1.1
    # via
    #   geopandas
    #   torchgeo
shellingham==1.5.4
    # via typer
six==1.16.0
    # via
    #   junit-xml
    #   lightly
    #   opencensus
    #   python-dateutil
    #   rfc3339-validator
    #   rouge-score
    #   segmentation-models-pytorch
smart-open==7.1.0
    # via ray
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via
    #   anyio
    #   httpx
sortedcontainers==2.4.0
    # via hypothesis
soundfile==0.12.1
    # via
    #   -r requirements/test.in
    #   librosa
    #   mistral-common
soxr==0.5.0.post1
    # via
    #   librosa
    #   mistral-common
sqlalchemy==2.0.41
    # via
    #   alembic
    #   mlflow
sqlitedict==2.1.0
    # via lm-eval
sqlparse==0.5.3
    # via mlflow-skinny
starlette==0.46.2
    # via
    #   fastapi
    #   schemathesis
    #   starlette-testclient
starlette-testclient==0.4.1
    # via schemathesis
statsmodels==0.14.4
    # via genai-perf
sympy==1.13.3
    # via
    #   einx
    #   torch
tabledata==1.3.3
    # via pytablewriter
tabulate==0.9.0
    # via sacrebleu
tcolorpy==0.1.6
    # via pytablewriter
tenacity==9.0.0
    # via
    #   lm-eval
    #   plotly
tensorboardx==2.6.4
    # via lightning
tensorizer==2.10.1
    # via -r requirements/test.in
terratorch==1.1rc2
    # via -r requirements/test.in
threadpoolctl==3.5.0
    # via scikit-learn
tifffile==2025.3.30
    # via
    #   scikit-image
    #   terratorch
tiktoken==0.7.0
    # via
    #   lm-eval
    #   mistral-common
timm==1.0.15
    # via
    #   -r requirements/test.in
    #   open-clip-torch
    #   segmentation-models-pytorch
    #   terratorch
    #   torchgeo
tokenizers==0.21.1
    # via
    #   -r requirements/test.in
    #   transformers
tomli==2.2.1
    # via schemathesis
tomli-w==1.2.0
    # via schemathesis
torch==2.7.1+cu128
    # via
    #   -r requirements/test.in
    #   accelerate
    #   bitsandbytes
    #   efficientnet-pytorch
    #   encodec
    #   fastsafetensors
    #   kornia
    #   lightly
    #   lightning
    #   lm-eval
    #   mamba-ssm
    #   mteb
    #   open-clip-torch
    #   peft
    #   pretrainedmodels
    #   pytorch-lightning
    #   runai-model-streamer
    #   segmentation-models-pytorch
    #   sentence-transformers
    #   tensorizer
    #   terratorch
    #   timm
    #   torchaudio
    #   torchgeo
    #   torchmetrics
    #   torchvision
    #   vector-quantize-pytorch
    #   vocos
torchaudio==2.7.1+cu128
    # via
    #   -r requirements/test.in
    #   encodec
    #   vocos
torchgeo==0.7.0
    # via terratorch
torchmetrics==1.7.4
    # via
    #   lightning
    #   pytorch-lightning
    #   terratorch
    #   torchgeo
torchvision==0.22.1+cu128
    # via
    #   -r requirements/test.in
    #   lightly
    #   open-clip-torch
    #   pretrainedmodels
    #   segmentation-models-pytorch
    #   terratorch
    #   timm
    #   torchgeo
tqdm==4.66.6
    # via
    #   datasets
    #   evaluate
    #   huggingface-hub
    #   lightly
    #   lightning
    #   lm-eval
    #   mteb
    #   nltk
    #   open-clip-torch
    #   peft
    #   pqdm
    #   pretrainedmodels
    #   pytorch-lightning
    #   segmentation-models-pytorch
    #   sentence-transformers
    #   tqdm-multiprocess
    #   transformers
tqdm-multiprocess==0.0.11
    # via lm-eval
transformers==4.53.2
    # via
    #   -r requirements/test.in
    #   genai-perf
    #   lm-eval
    #   mamba-ssm
    #   peft
    #   sentence-transformers
    #   transformers-stream-generator
transformers-stream-generator==0.0.5
    # via -r requirements/test.in
triton==3.3.1
    # via
    #   mamba-ssm
    #   torch
tritonclient==2.51.0
    # via
    #   -r requirements/test.in
    #   genai-perf
typepy==1.3.2
    # via
    #   dataproperty
    #   pytablewriter
    #   tabledata
typer==0.15.2
    # via fastsafetensors
types-python-dateutil==2.9.0.20241206
    # via arrow
typeshed-client==2.8.2
    # via jsonargparse
typing-extensions==4.12.2
    # via
    #   albumentations
    #   alembic
    #   fastapi
    #   graphene
    #   huggingface-hub
    #   librosa
    #   lightning
    #   lightning-utilities
    #   mistral-common
    #   mlflow-skinny
    #   mteb
    #   opentelemetry-api
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
    #   pqdm
    #   pydantic
    #   pydantic-core
    #   pydantic-extra-types
    #   pytorch-lightning
    #   sqlalchemy
    #   torch
    #   torchgeo
    #   typer
    #   typeshed-client
    #   typing-inspection
typing-inspection==0.4.1
    # via pydantic
tzdata==2024.2
    # via pandas
uri-template==1.3.0
    # via jsonschema
urllib3==2.2.3
    # via
    #   blobfile
    #   botocore
    #   docker
    #   lightly
    #   requests
    #   responses
    #   tritonclient
uvicorn==0.35.0
    # via mlflow-skinny
vector-quantize-pytorch==1.21.2
    # via -r requirements/test.in
virtualenv==20.31.2
    # via ray
vocos==0.1.0
    # via -r requirements/test.in
wcwidth==0.2.13
    # via ftfy
webcolors==24.11.1
    # via jsonschema
werkzeug==3.1.3
    # via
    #   flask
    #   schemathesis
word2number==1.1
    # via lm-eval
wrapt==1.17.2
    # via smart-open
xarray==2025.7.1
    # via rioxarray
xxhash==3.5.0
    # via
    #   datasets
    #   evaluate
yarl==1.17.1
    # via
    #   aiohttp
    #   schemathesis
zipp==3.23.0
    # via importlib-metadata
zstandard==0.23.0
    # via lm-eval
