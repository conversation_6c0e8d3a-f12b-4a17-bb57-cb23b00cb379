# Read the Docs configuration file
# See https://docs.readthedocs.io/en/stable/config-file/v2.html for details

version: 2

build:
  os: ubuntu-22.04
  tools:
    python: "3.12"
  jobs:
    post_checkout:
      - git fetch --unshallow || true

mkdocs:
  configuration: mkdocs.yaml

# Optionally declare the Python requirements required to build your docs
python:
  install:
    - requirements: requirements/docs.txt
