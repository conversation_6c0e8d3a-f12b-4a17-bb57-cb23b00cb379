#!/usr/bin/env python3
"""
Test script to verify that the tool parser plugin fix works correctly.
This script simulates the argument parsing and validation flow.
"""

import sys
import os
import tempfile

# Add the vllm directory to the path
sys.path.insert(0, '/Users/<USER>/py-workspace/github.com/lengrongfu/vllm')

from vllm.entrypoints.openai.cli_args import make_arg_parser
from vllm.entrypoints.openai.api_server import validate_api_server_args
from vllm.entrypoints.openai.tool_parsers import ToolParserManager
from vllm.utils import FlexibleArgumentParser

def test_tool_parser_plugin():
    """Test that custom tool parser plugins can be loaded and validated."""
    
    # Create a temporary tool parser plugin file
    plugin_content = '''
from typing import Any, Optional, Union
from vllm.entrypoints.openai.tool_parsers import Tool<PERSON>arserManager, ToolParser
from vllm.entrypoints.openai.protocol import ExtractedToolCallInformation
from vllm.entrypoints.openai.protocol import DeltaMessage
from vllm.entrypoints.openai.protocol import ChatCompletionRequest
from vllm.transformers_utils.tokenizer import AnyTokenizer

@ToolParserManager.register_module(["test_example"])
class TestExampleToolParser(ToolParser):
    def __init__(self, tokenizer: AnyTokenizer):
        super().__init__(tokenizer)

    def adjust_request(self, request: ChatCompletionRequest) -> ChatCompletionRequest:
        return request

    def extract_tool_calls_streaming(self, *args, **kwargs) -> Union[DeltaMessage, None]:
        return DeltaMessage(role="assistant", content="", tool_calls=[])

    def extract_tool_calls(self, *args, **kwargs) -> ExtractedToolCallInformation:
        text = args[0] if args else ""
        return ExtractedToolCallInformation(tools_called=False, tool_calls=[], content=text)
'''
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(plugin_content)
        plugin_path = f.name
    
    try:
        # Test 1: Check that argument parsing works without choices restriction
        print("Test 1: Argument parsing with custom tool parser...")
        parser = FlexibleArgumentParser(description="Test parser")
        parser = make_arg_parser(parser)
        
        # This should not fail now (previously would fail due to choices restriction)
        args = parser.parse_args([
            "test-model",
            "--enable-auto-tool-choice",
            "--tool-call-parser", "test_example",
            "--tool-parser-plugin", plugin_path
        ])
        print("✓ Argument parsing successful")
        
        # Test 2: Check that plugin loading works
        print("Test 2: Plugin loading...")
        print(f"Tool parsers before loading: {list(ToolParserManager.tool_parsers.keys())}")
        
        # Simulate the plugin loading that happens in setup_server
        if args.tool_parser_plugin and len(args.tool_parser_plugin) > 3:
            ToolParserManager.import_tool_parser(args.tool_parser_plugin)
        
        print(f"Tool parsers after loading: {list(ToolParserManager.tool_parsers.keys())}")
        print("✓ Plugin loading successful")
        
        # Test 3: Check that validation works
        print("Test 3: Validation...")
        try:
            validate_api_server_args(args)
            print("✓ Validation successful")
        except Exception as e:
            print(f"✗ Validation failed: {e}")
            return False
        
        # Test 4: Check that invalid parser still fails
        print("Test 4: Invalid parser validation...")
        args.tool_call_parser = "nonexistent_parser"
        try:
            validate_api_server_args(args)
            print("✗ Validation should have failed for invalid parser")
            return False
        except KeyError as e:
            print(f"✓ Validation correctly failed for invalid parser: {e}")
        
        print("\nAll tests passed! The fix is working correctly.")
        return True
        
    finally:
        # Clean up
        os.unlink(plugin_path)

if __name__ == "__main__":
    success = test_tool_parser_plugin()
    sys.exit(0 if success else 1)
