#!/usr/bin/env python3
"""
Simple test to check if the argument parser accepts custom tool parsers.
"""

import argparse
import sys
import os

# Add the vllm directory to the path
sys.path.insert(0, '/Users/<USER>/py-workspace/github.com/lengrongfu/vllm')

def test_argument_parsing():
    """Test that the argument parser no longer restricts tool parser choices."""
    try:
        # Import just the argument parsing parts
        from vllm.utils import FlexibleArgumentParser
        from vllm.entrypoints.openai.cli_args import FrontendArgs
        
        # Create a parser
        parser = FlexibleArgumentParser(description="Test parser")
        parser = FrontendArgs.add_cli_args(parser)
        
        # Try to parse arguments with a custom tool parser
        # This should work now (previously would fail due to choices restriction)
        test_args = [
            "--enable-auto-tool-choice",
            "--tool-call-parser", "custom_example",
            "--tool-parser-plugin", "./example_tool_parser.py"
        ]
        
        args = parser.parse_args(test_args)
        print("✓ Argument parsing successful!")
        print(f"  tool_call_parser: {args.tool_call_parser}")
        print(f"  tool_parser_plugin: {args.tool_parser_plugin}")
        print(f"  enable_auto_tool_choice: {args.enable_auto_tool_choice}")
        
        return True
        
    except SystemExit as e:
        if e.code != 0:
            print(f"✗ Argument parsing failed with exit code: {e.code}")
            return False
        return True
    except Exception as e:
        print(f"✗ Argument parsing failed with exception: {e}")
        return False

if __name__ == "__main__":
    success = test_argument_parsing()
    print(f"\nTest {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
